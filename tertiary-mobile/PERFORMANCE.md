# ⚡ **Performance Optimization Guide**

## **Overview**

This document outlines performance optimization strategies implemented in the tertiary mobile app, including lazy loading, virtual scrolling, memory management, and state optimization.

## **🎯 Key Performance Concepts**

### **1. Lazy Loading**

**Definition**: Load data only when needed, not all at once.

**Implementation Strategy**:
```dart
// Load basic course list first
Future<List<CourseBasic>> loadCourseList() async {
  return await courseRepository.fetchBasicCourses();
}

// Load detailed course info on demand
Future<CourseDetail> loadCourseDetail(String courseId) async {
  return await courseRepository.fetchCourseDetail(courseId);
}
```

**Benefits**:
- Faster initial load times
- Reduced memory usage
- Better user experience
- Lower network usage

### **2. Virtual Scrolling**

**Definition**: Render only visible list items plus a small buffer.

**Flutter Implementation**:
```dart
ListView.builder(
  itemCount: courses.length,
  itemBuilder: (context, index) {
    // Only builds visible items
    return CourseListItem(course: courses[index]);
  },
)
```

**Advanced Virtual Scrolling**:
```dart
// For very large lists (1000+ items)
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';

MasonryGridView.builder(
  gridDelegate: SliverSimpleGridDelegateWithFixedCrossAxisCount(
    crossAxisCount: 2,
  ),
  itemCount: courses.length,
  itemBuilder: (context, index) => CourseCard(courses[index]),
)
```

### **3. Memory Usage Optimization**

**Strategies**:

1. **Proper Widget Disposal**:
```dart
class CourseListScreen extends ConsumerStatefulWidget {
  @override
  ConsumerState<CourseListScreen> createState() => _CourseListScreenState();
}

class _CourseListScreenState extends ConsumerState<CourseListScreen> {
  late ScrollController _scrollController;
  
  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
  }
  
  @override
  void dispose() {
    _scrollController.dispose(); // Prevent memory leaks
    super.dispose();
  }
}
```

2. **Image Memory Management**:
```dart
// Use cached network images with memory limits
CachedNetworkImage(
  imageUrl: imageUrl,
  memCacheWidth: 300, // Limit memory usage
  memCacheHeight: 200,
  placeholder: (context, url) => CircularProgressIndicator(),
  errorWidget: (context, url, error) => Icon(Icons.error),
)
```

3. **Provider Lifecycle Management**:
```dart
// Auto-dispose providers when not needed
@riverpod
class CourseListNotifier extends _$CourseListNotifier {
  @override
  Future<List<Course>> build() async {
    // Auto-disposed when no longer watched
    return fetchCourses();
  }
  
  @override
  void dispose() {
    // Clean up resources
    super.dispose();
  }
}
```

## **📊 Performance Benchmarks**

### **Benchmark Categories**

1. **App Startup Time**
   - Cold start: < 3 seconds
   - Warm start: < 1 second
   - Hot reload: < 500ms

2. **List Scrolling Performance**
   - Target: 60 FPS consistently
   - Large lists (1000+ items): No frame drops
   - Memory usage: < 100MB for large lists

3. **Network Performance**
   - API response time: < 2 seconds
   - Cache hit rate: > 80%
   - Offline functionality: 100% for cached data

4. **Memory Usage**
   - Base app: < 50MB
   - With large datasets: < 150MB
   - Memory leaks: 0 detected

### **Performance Monitoring**

```dart
// Performance monitoring utility
class PerformanceMonitor {
  static void measureExecutionTime(String operation, Function() callback) {
    final stopwatch = Stopwatch()..start();
    callback();
    stopwatch.stop();
    logger.i('$operation took ${stopwatch.elapsedMilliseconds}ms');
  }
  
  static Future<T> measureAsyncOperation<T>(
    String operation, 
    Future<T> Function() callback
  ) async {
    final stopwatch = Stopwatch()..start();
    final result = await callback();
    stopwatch.stop();
    logger.i('$operation took ${stopwatch.elapsedMilliseconds}ms');
    return result;
  }
}

// Usage
final courses = await PerformanceMonitor.measureAsyncOperation(
  'Fetch Courses',
  () => courseRepository.fetchCourses(),
);
```

## **🔄 State Management Optimization**

### **1. State Persistence**

**Implementation**:
```dart
@riverpod
class AppStateNotifier extends _$AppStateNotifier {
  @override
  AppState build() {
    // Load persisted state on app start
    _loadPersistedState();
    return AppState.initial();
  }
  
  void _loadPersistedState() async {
    final prefs = await SharedPreferences.getInstance();
    final selectedSessionId = prefs.getInt('selected_session_id');
    if (selectedSessionId != null) {
      state = state.copyWith(selectedSessionId: selectedSessionId);
    }
  }
  
  void updateSelectedSession(int sessionId) {
    state = state.copyWith(selectedSessionId: sessionId);
    _persistState();
  }
  
  void _persistState() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('selected_session_id', state.selectedSessionId);
  }
}
```

### **2. Optimistic Updates**

**Pattern**:
```dart
Future<void> registerForCourse(Course course) async {
  // 1. Update UI immediately (optimistic)
  state = state.copyWith(
    registeredCourses: [...state.registeredCourses, course],
    isLoading: true,
  );
  
  try {
    // 2. Make API call
    await courseRepository.registerForCourse(course.id);
    
    // 3. Update with server response
    state = state.copyWith(isLoading: false);
  } catch (error) {
    // 4. Revert on failure
    state = state.copyWith(
      registeredCourses: state.registeredCourses
          .where((c) => c.id != course.id)
          .toList(),
      isLoading: false,
      error: error.toString(),
    );
    
    // Show error to user
    _showErrorSnackbar('Failed to register for course');
  }
}
```

### **3. Provider Lifecycle Management**

**Auto-Dispose Pattern**:
```dart
// Automatically dispose when no longer needed
@riverpod
Future<List<Course>> courses(CoursesRef ref) async {
  // Keep alive for 5 minutes after last use
  ref.keepAlive();
  Timer(Duration(minutes: 5), () {
    ref.invalidateSelf();
  });
  
  return await fetchCourses();
}

// Manual lifecycle control
@riverpod
class CourseDetailNotifier extends _$CourseDetailNotifier {
  Timer? _disposeTimer;
  
  @override
  Future<CourseDetail?> build(String courseId) async {
    ref.onDispose(() {
      _disposeTimer?.cancel();
    });
    
    return await fetchCourseDetail(courseId);
  }
  
  void scheduleDispose() {
    _disposeTimer = Timer(Duration(minutes: 2), () {
      ref.invalidateSelf();
    });
  }
}
```

### **4. State Recovery Mechanisms**

**Error Recovery**:
```dart
@riverpod
class RobustStateNotifier extends _$RobustStateNotifier {
  @override
  AppState build() {
    return _loadStateWithFallback();
  }
  
  AppState _loadStateWithFallback() {
    try {
      // Try to load saved state
      return _loadPersistedState();
    } catch (error) {
      logger.w('Failed to load persisted state: $error');
      // Fall back to default state
      return AppState.defaultState();
    }
  }
  
  void recoverFromError() {
    try {
      // Attempt to recover
      state = _loadStateWithFallback();
    } catch (error) {
      // Last resort: reset to clean state
      state = AppState.clean();
    }
  }
}
```

## **🚀 Performance Best Practices**

### **1. List Optimization**
- Use `ListView.builder` for large lists
- Implement proper `itemExtent` when items have fixed height
- Use `RepaintBoundary` for complex list items
- Implement pull-to-refresh efficiently

### **2. Image Optimization**
- Use appropriate image formats (WebP when possible)
- Implement progressive loading for large images
- Cache images with size limits
- Use placeholders and error widgets

### **3. Network Optimization**
- Implement request deduplication
- Use connection pooling
- Implement retry logic with exponential backoff
- Cache responses appropriately

### **4. Memory Management**
- Dispose controllers and streams properly
- Use weak references for large objects
- Implement memory pressure handling
- Monitor memory usage in development

### **5. State Management**
- Keep state minimal and normalized
- Use computed properties instead of storing derived data
- Implement proper error boundaries
- Use optimistic updates for better UX

## **📈 Monitoring and Debugging**

### **Performance Profiling**
```bash
# Profile app performance
flutter run --profile

# Analyze memory usage
flutter run --profile --trace-startup

# Check for memory leaks
flutter run --profile --enable-memory-profiling
```

### **Performance Widgets**
```dart
// Wrap expensive widgets with performance monitoring
PerformanceOverlay.allEnabled(), // Shows FPS and memory
```

This performance optimization guide provides a comprehensive approach to building a fast, responsive, and memory-efficient Flutter application.
