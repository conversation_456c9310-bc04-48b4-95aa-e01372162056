import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'dart:math' as math;

/// A virtual list view that only renders visible items for optimal performance
/// with large datasets
class VirtualListView<T> extends StatefulWidget {
  final List<T> items;
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  final double? itemExtent;
  final EdgeInsetsGeometry? padding;
  final ScrollController? controller;
  final ScrollPhysics? physics;
  final bool shrinkWrap;
  final int? bufferSize;
  final void Function(int firstVisibleIndex, int lastVisibleIndex)? onVisibleRangeChanged;

  const VirtualListView({
    super.key,
    required this.items,
    required this.itemBuilder,
    this.itemExtent,
    this.padding,
    this.controller,
    this.physics,
    this.shrinkWrap = false,
    this.bufferSize = 5,
    this.onVisibleRangeChanged,
  });

  @override
  State<VirtualListView<T>> createState() => _VirtualListViewState<T>();
}

class _VirtualListViewState<T> extends State<VirtualListView<T>> {
  late ScrollController _controller;
  int _firstVisibleIndex = 0;
  int _lastVisibleIndex = 0;
  double _estimatedItemHeight = 50.0;
  final Map<int, double> _itemHeights = {};
  bool _isScrolling = false;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? ScrollController();
    _controller.addListener(_onScroll);
    
    // Initialize estimated item height
    if (widget.itemExtent != null) {
      _estimatedItemHeight = widget.itemExtent!;
    }
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _updateVisibleRange();
    });
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    } else {
      _controller.removeListener(_onScroll);
    }
    super.dispose();
  }

  void _onScroll() {
    if (!_isScrolling) {
      _isScrolling = true;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _updateVisibleRange();
        _isScrolling = false;
      });
    }
  }

  void _updateVisibleRange() {
    if (!mounted) return;

    final renderBox = context.findRenderObject() as RenderBox?;
    if (renderBox == null) return;

    final viewportHeight = renderBox.size.height;
    final scrollOffset = _controller.offset;
    final bufferSize = widget.bufferSize ?? 5;

    // Calculate visible range based on scroll position and item heights
    int firstVisible = 0;
    int lastVisible = widget.items.length - 1;

    if (widget.itemExtent != null) {
      // Fixed height items - easy calculation
      final itemHeight = widget.itemExtent!;
      firstVisible = math.max(0, (scrollOffset / itemHeight).floor() - bufferSize);
      lastVisible = math.min(
        widget.items.length - 1,
        ((scrollOffset + viewportHeight) / itemHeight).ceil() + bufferSize,
      );
    } else {
      // Variable height items - use estimated heights
      double currentOffset = 0;
      firstVisible = 0;
      
      // Find first visible item
      for (int i = 0; i < widget.items.length; i++) {
        final itemHeight = _itemHeights[i] ?? _estimatedItemHeight;
        if (currentOffset + itemHeight > scrollOffset) {
          firstVisible = math.max(0, i - bufferSize);
          break;
        }
        currentOffset += itemHeight;
      }
      
      // Find last visible item
      currentOffset = _getOffsetForIndex(firstVisible);
      lastVisible = firstVisible;
      
      for (int i = firstVisible; i < widget.items.length; i++) {
        if (currentOffset > scrollOffset + viewportHeight) {
          lastVisible = math.min(widget.items.length - 1, i + bufferSize);
          break;
        }
        final itemHeight = _itemHeights[i] ?? _estimatedItemHeight;
        currentOffset += itemHeight;
        lastVisible = i;
      }
    }

    if (firstVisible != _firstVisibleIndex || lastVisible != _lastVisibleIndex) {
      setState(() {
        _firstVisibleIndex = firstVisible;
        _lastVisibleIndex = lastVisible;
      });
      
      widget.onVisibleRangeChanged?.call(firstVisible, lastVisible);
    }
  }

  double _getOffsetForIndex(int index) {
    if (widget.itemExtent != null) {
      return index * widget.itemExtent!;
    }
    
    double offset = 0;
    for (int i = 0; i < index; i++) {
      offset += _itemHeights[i] ?? _estimatedItemHeight;
    }
    return offset;
  }

  double _getTotalHeight() {
    if (widget.itemExtent != null) {
      return widget.items.length * widget.itemExtent!;
    }
    
    double totalHeight = 0;
    for (int i = 0; i < widget.items.length; i++) {
      totalHeight += _itemHeights[i] ?? _estimatedItemHeight;
    }
    return totalHeight;
  }

  void _measureItem(int index, double height) {
    if (_itemHeights[index] != height) {
      _itemHeights[index] = height;
      
      // Update estimated height based on measured items
      if (_itemHeights.isNotEmpty) {
        final totalMeasured = _itemHeights.values.reduce((a, b) => a + b);
        _estimatedItemHeight = totalMeasured / _itemHeights.length;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.items.isEmpty) {
      return const SizedBox.shrink();
    }

    // For small lists, use regular ListView for simplicity
    if (widget.items.length <= 50) {
      return ListView.builder(
        controller: _controller,
        padding: widget.padding,
        physics: widget.physics,
        shrinkWrap: widget.shrinkWrap,
        itemCount: widget.items.length,
        itemExtent: widget.itemExtent,
        itemBuilder: (context, index) {
          return widget.itemBuilder(context, widget.items[index], index);
        },
      );
    }

    // Virtual scrolling for large lists
    final visibleItems = <Widget>[];
    final topOffset = _getOffsetForIndex(_firstVisibleIndex);
    final bottomOffset = _getTotalHeight() - _getOffsetForIndex(_lastVisibleIndex + 1);

    // Add top spacer
    if (topOffset > 0) {
      visibleItems.add(SizedBox(height: topOffset));
    }

    // Add visible items
    for (int i = _firstVisibleIndex; i <= _lastVisibleIndex && i < widget.items.length; i++) {
      final item = widget.items[i];
      Widget itemWidget = widget.itemBuilder(context, item, i);
      
      // Wrap with measurement widget for variable height items
      if (widget.itemExtent == null) {
        itemWidget = _MeasurableItem(
          index: i,
          onMeasured: _measureItem,
          child: itemWidget,
        );
      }
      
      visibleItems.add(itemWidget);
    }

    // Add bottom spacer
    if (bottomOffset > 0) {
      visibleItems.add(SizedBox(height: bottomOffset));
    }

    return ListView(
      controller: _controller,
      padding: widget.padding,
      physics: widget.physics,
      shrinkWrap: widget.shrinkWrap,
      children: visibleItems,
    );
  }
}

/// Widget that measures its child's height for virtual scrolling
class _MeasurableItem extends StatefulWidget {
  final int index;
  final void Function(int index, double height) onMeasured;
  final Widget child;

  const _MeasurableItem({
    required this.index,
    required this.onMeasured,
    required this.child,
  });

  @override
  State<_MeasurableItem> createState() => _MeasurableItemState();
}

class _MeasurableItemState extends State<_MeasurableItem> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _measureHeight();
    });
  }

  void _measureHeight() {
    final renderBox = context.findRenderObject() as RenderBox?;
    if (renderBox != null) {
      final height = renderBox.size.height;
      widget.onMeasured(widget.index, height);
    }
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}

/// Extension to make VirtualListView easier to use
extension VirtualListViewExtension<T> on List<T> {
  Widget toVirtualListView({
    required Widget Function(BuildContext context, T item, int index) itemBuilder,
    double? itemExtent,
    EdgeInsetsGeometry? padding,
    ScrollController? controller,
    ScrollPhysics? physics,
    bool shrinkWrap = false,
    int? bufferSize,
    void Function(int firstVisibleIndex, int lastVisibleIndex)? onVisibleRangeChanged,
  }) {
    return VirtualListView<T>(
      items: this,
      itemBuilder: itemBuilder,
      itemExtent: itemExtent,
      padding: padding,
      controller: controller,
      physics: physics,
      shrinkWrap: shrinkWrap,
      bufferSize: bufferSize,
      onVisibleRangeChanged: onVisibleRangeChanged,
    );
  }
}
