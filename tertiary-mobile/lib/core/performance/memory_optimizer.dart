import 'dart:async';
import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:tertiary_mobile/core/utils/logger.dart';

part 'memory_optimizer.g.dart';

@riverpod
MemoryOptimizer memoryOptimizer(Ref ref) {
  return MemoryOptimizer();
}

/// Manages memory usage optimization and monitoring
class MemoryOptimizer {
  Timer? _memoryMonitorTimer;
  final List<WeakReference<Object>> _trackedObjects = [];
  final Map<String, int> _memoryStats = {};
  
  static const int _memoryWarningThresholdMB = 100;
  static const int _memoryCriticalThresholdMB = 150;

  /// Initialize memory monitoring
  void initialize() {
    if (kDebugMode) {
      _startMemoryMonitoring();
    }
    _setupMemoryPressureHandling();
    logger.i('Memory optimizer initialized');
  }

  /// Start periodic memory monitoring in debug mode
  void _startMemoryMonitoring() {
    _memoryMonitorTimer = Timer.periodic(
      Duration(seconds: 30),
      (_) => _checkMemoryUsage(),
    );
  }

  /// Setup memory pressure handling
  void _setupMemoryPressureHandling() {
    SystemChannels.system.setMessageHandler((message) async {
      if (message?['type'] == 'memoryPressure') {
        await handleMemoryPressure();
      }
      return null;
    });
  }

  /// Check current memory usage
  Future<void> _checkMemoryUsage() async {
    try {
      final info = await developer.Service.getInfo();
      if (info.serverUri != null) {
        // In debug mode, we can get more detailed memory info
        _logMemoryStats();
      }
      
      // Clean up weak references
      _cleanupWeakReferences();
      
    } catch (e) {
      logger.w('Failed to check memory usage: $e');
    }
  }

  /// Log memory statistics
  void _logMemoryStats() {
    final stats = _getMemoryStats();
    logger.d('Memory Stats: ${stats.entries.map((e) => '${e.key}: ${e.value}').join(', ')}');
    
    // Check for memory warnings
    final totalMB = stats['totalMB'] ?? 0;
    if (totalMB > _memoryCriticalThresholdMB) {
      logger.w('CRITICAL: Memory usage is ${totalMB}MB (threshold: ${_memoryCriticalThresholdMB}MB)');
      handleMemoryPressure();
    } else if (totalMB > _memoryWarningThresholdMB) {
      logger.w('WARNING: Memory usage is ${totalMB}MB (threshold: ${_memoryWarningThresholdMB}MB)');
    }
  }

  /// Get memory statistics
  Map<String, int> _getMemoryStats() {
    // This is a simplified version - in a real app you might use
    // platform-specific code to get actual memory usage
    return {
      'trackedObjects': _trackedObjects.length,
      'aliveObjects': _trackedObjects.where((ref) => ref.target != null).length,
      'totalMB': 0, // Would be populated with actual memory usage
    };
  }

  /// Handle memory pressure by cleaning up resources
  Future<void> handleMemoryPressure() async {
    logger.w('Handling memory pressure - cleaning up resources');
    
    try {
      // 1. Clean up weak references
      _cleanupWeakReferences();
      
      // 2. Clear image caches
      await _clearImageCaches();
      
      // 3. Force garbage collection
      _forceGarbageCollection();
      
      // 4. Notify listeners about memory pressure
      _notifyMemoryPressure();
      
      logger.i('Memory pressure handling completed');
    } catch (e) {
      logger.e('Error handling memory pressure: $e');
    }
  }

  /// Clear image caches to free memory
  Future<void> _clearImageCaches() async {
    try {
      // Clear Flutter's image cache
      PaintingBinding.instance.imageCache.clear();
      PaintingBinding.instance.imageCache.clearLiveImages();
      
      logger.d('Cleared image caches');
    } catch (e) {
      logger.e('Error clearing image caches: $e');
    }
  }

  /// Force garbage collection (debug mode only)
  void _forceGarbageCollection() {
    if (kDebugMode) {
      // Force GC in debug mode
      developer.Service.getInfo().then((_) {
        // This triggers GC indirectly
      });
    }
  }

  /// Notify about memory pressure (could trigger UI updates)
  void _notifyMemoryPressure() {
    // In a real implementation, you might use a stream or callback
    // to notify other parts of the app about memory pressure
  }

  /// Track an object for memory monitoring
  void trackObject(Object object, {String? category}) {
    if (kDebugMode) {
      _trackedObjects.add(WeakReference(object));
      final cat = category ?? object.runtimeType.toString();
      _memoryStats[cat] = (_memoryStats[cat] ?? 0) + 1;
    }
  }

  /// Clean up dead weak references
  void _cleanupWeakReferences() {
    _trackedObjects.removeWhere((ref) => ref.target == null);
  }

  /// Optimize list performance by limiting rendered items
  int optimizeListItemCount(int totalItems, {int maxItems = 1000}) {
    if (totalItems <= maxItems) return totalItems;
    
    logger.w('Large list detected ($totalItems items), limiting to $maxItems for performance');
    return maxItems;
  }

  /// Create a memory-efficient list builder
  Widget Function(BuildContext, int) createOptimizedListBuilder<T>(
    List<T> items,
    Widget Function(BuildContext, T, int) itemBuilder, {
    int? maxItems,
  }) {
    final effectiveMaxItems = maxItems ?? optimizeListItemCount(items.length);
    final effectiveItems = items.take(effectiveMaxItems).toList();
    
    return (context, index) {
      if (index >= effectiveItems.length) {
        return SizedBox.shrink();
      }
      return itemBuilder(context, effectiveItems[index], index);
    };
  }

  /// Dispose of resources when no longer needed
  void dispose() {
    _memoryMonitorTimer?.cancel();
    _trackedObjects.clear();
    _memoryStats.clear();
    logger.d('Memory optimizer disposed');
  }

  /// Get current memory optimization recommendations
  List<MemoryOptimizationRecommendation> getOptimizationRecommendations() {
    final recommendations = <MemoryOptimizationRecommendation>[];
    
    // Check tracked objects
    final aliveObjects = _trackedObjects.where((ref) => ref.target != null).length;
    if (aliveObjects > 100) {
      recommendations.add(MemoryOptimizationRecommendation(
        type: OptimizationType.reduceObjectCount,
        description: 'High number of tracked objects ($aliveObjects)',
        priority: RecommendationPriority.medium,
      ));
    }
    
    // Check image cache
    final imageCache = PaintingBinding.instance.imageCache;
    if (imageCache.currentSize > 50 * 1024 * 1024) { // 50MB
      recommendations.add(MemoryOptimizationRecommendation(
        type: OptimizationType.clearImageCache,
        description: 'Image cache is large (${(imageCache.currentSize / 1024 / 1024).toStringAsFixed(1)}MB)',
        priority: RecommendationPriority.high,
      ));
    }
    
    return recommendations;
  }

  /// Apply optimization recommendations
  Future<void> applyOptimizations(List<MemoryOptimizationRecommendation> recommendations) async {
    for (final recommendation in recommendations) {
      try {
        switch (recommendation.type) {
          case OptimizationType.clearImageCache:
            await _clearImageCaches();
            break;
          case OptimizationType.reduceObjectCount:
            _cleanupWeakReferences();
            break;
          case OptimizationType.forceGarbageCollection:
            _forceGarbageCollection();
            break;
        }
        logger.d('Applied optimization: ${recommendation.type}');
      } catch (e) {
        logger.e('Failed to apply optimization ${recommendation.type}: $e');
      }
    }
  }
}

/// Memory optimization recommendation
class MemoryOptimizationRecommendation {
  final OptimizationType type;
  final String description;
  final RecommendationPriority priority;

  MemoryOptimizationRecommendation({
    required this.type,
    required this.description,
    required this.priority,
  });
}

/// Types of memory optimizations
enum OptimizationType {
  clearImageCache,
  reduceObjectCount,
  forceGarbageCollection,
}

/// Priority levels for recommendations
enum RecommendationPriority {
  low,
  medium,
  high,
  critical,
}

/// Extension for easier memory tracking
extension MemoryTrackingExtension on Object {
  void trackMemory(MemoryOptimizer optimizer, {String? category}) {
    optimizer.trackObject(this, category: category);
  }
}
