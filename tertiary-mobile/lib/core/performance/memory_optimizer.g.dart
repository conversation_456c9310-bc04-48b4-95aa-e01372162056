// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'memory_optimizer.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$memoryOptimizerHash() => r'6f6610bc796a6f39a395ba499b55172c9820e0b9';

/// See also [memoryOptimizer].
@ProviderFor(memoryOptimizer)
final memoryOptimizerProvider = AutoDisposeProvider<MemoryOptimizer>.internal(
  memoryOptimizer,
  name: r'memoryOptimizerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$memoryOptimizerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef MemoryOptimizerRef = AutoDisposeProviderRef<MemoryOptimizer>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
