import 'dart:convert';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tertiary_mobile/core/utils/logger.dart';

part 'state_persistence_manager.g.dart';

@riverpod
StatePersistenceManager statePersistenceManager(Ref ref) {
  return StatePersistenceManager();
}

/// Manages persistence of critical app state to survive app restarts
class StatePersistenceManager {
  static const String _keyPrefix = 'app_state_';
  static const String _selectedSessionKey = '${_keyPrefix}selected_session';
  static const String _selectedSemesterKey = '${_keyPrefix}selected_semester';
  static const String _userPreferencesKey = '${_keyPrefix}user_preferences';
  static const String _lastSyncTimestampKey = '${_keyPrefix}last_sync';
  static const String _appVersionKey = '${_keyPrefix}app_version';

  SharedPreferences? _prefs;

  /// Initialize the persistence manager
  Future<void> initialize() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      logger.i('State persistence manager initialized');
    } catch (e) {
      logger.e('Failed to initialize state persistence manager: $e');
    }
  }

  /// Get SharedPreferences instance, initializing if needed
  Future<SharedPreferences> get _preferences async {
    _prefs ??= await SharedPreferences.getInstance();
    return _prefs!;
  }

  /// Save selected session ID
  Future<void> saveSelectedSession(int sessionId) async {
    try {
      final prefs = await _preferences;
      await prefs.setInt(_selectedSessionKey, sessionId);
      logger.d('Saved selected session: $sessionId');
    } catch (e) {
      logger.e('Failed to save selected session: $e');
    }
  }

  /// Load selected session ID
  Future<int?> loadSelectedSession() async {
    try {
      final prefs = await _preferences;
      final sessionId = prefs.getInt(_selectedSessionKey);
      logger.d('Loaded selected session: $sessionId');
      return sessionId;
    } catch (e) {
      logger.e('Failed to load selected session: $e');
      return null;
    }
  }

  /// Save selected semester ID
  Future<void> saveSelectedSemester(int semesterId) async {
    try {
      final prefs = await _preferences;
      await prefs.setInt(_selectedSemesterKey, semesterId);
      logger.d('Saved selected semester: $semesterId');
    } catch (e) {
      logger.e('Failed to save selected semester: $e');
    }
  }

  /// Load selected semester ID
  Future<int?> loadSelectedSemester() async {
    try {
      final prefs = await _preferences;
      final semesterId = prefs.getInt(_selectedSemesterKey);
      logger.d('Loaded selected semester: $semesterId');
      return semesterId;
    } catch (e) {
      logger.e('Failed to load selected semester: $e');
      return null;
    }
  }

  /// Save user preferences as JSON
  Future<void> saveUserPreferences(Map<String, dynamic> preferences) async {
    try {
      final prefs = await _preferences;
      final jsonString = jsonEncode(preferences);
      await prefs.setString(_userPreferencesKey, jsonString);
      logger.d('Saved user preferences: ${preferences.keys.join(', ')}');
    } catch (e) {
      logger.e('Failed to save user preferences: $e');
    }
  }

  /// Load user preferences from JSON
  Future<Map<String, dynamic>> loadUserPreferences() async {
    try {
      final prefs = await _preferences;
      final jsonString = prefs.getString(_userPreferencesKey);
      if (jsonString != null) {
        final preferences = jsonDecode(jsonString) as Map<String, dynamic>;
        logger.d('Loaded user preferences: ${preferences.keys.join(', ')}');
        return preferences;
      }
    } catch (e) {
      logger.e('Failed to load user preferences: $e');
    }
    return {};
  }

  /// Save last sync timestamp
  Future<void> saveLastSyncTimestamp(DateTime timestamp) async {
    try {
      final prefs = await _preferences;
      await prefs.setString(_lastSyncTimestampKey, timestamp.toIso8601String());
      logger.d('Saved last sync timestamp: $timestamp');
    } catch (e) {
      logger.e('Failed to save last sync timestamp: $e');
    }
  }

  /// Load last sync timestamp
  Future<DateTime?> loadLastSyncTimestamp() async {
    try {
      final prefs = await _preferences;
      final timestampString = prefs.getString(_lastSyncTimestampKey);
      if (timestampString != null) {
        final timestamp = DateTime.parse(timestampString);
        logger.d('Loaded last sync timestamp: $timestamp');
        return timestamp;
      }
    } catch (e) {
      logger.e('Failed to load last sync timestamp: $e');
    }
    return null;
  }

  /// Save app version for migration detection
  Future<void> saveAppVersion(String version) async {
    try {
      final prefs = await _preferences;
      await prefs.setString(_appVersionKey, version);
      logger.d('Saved app version: $version');
    } catch (e) {
      logger.e('Failed to save app version: $e');
    }
  }

  /// Load app version
  Future<String?> loadAppVersion() async {
    try {
      final prefs = await _preferences;
      final version = prefs.getString(_appVersionKey);
      logger.d('Loaded app version: $version');
      return version;
    } catch (e) {
      logger.e('Failed to load app version: $e');
      return null;
    }
  }

  /// Check if this is a fresh install (no persisted state)
  Future<bool> isFreshInstall() async {
    try {
      final prefs = await _preferences;
      return !prefs.containsKey(_appVersionKey);
    } catch (e) {
      logger.e('Failed to check fresh install status: $e');
      return true;
    }
  }

  /// Clear all persisted state (useful for logout or reset)
  Future<void> clearAllState() async {
    try {
      final prefs = await _preferences;
      final keys = prefs.getKeys().where((key) => key.startsWith(_keyPrefix));
      for (final key in keys) {
        await prefs.remove(key);
      }
      logger.i('Cleared all persisted state');
    } catch (e) {
      logger.e('Failed to clear persisted state: $e');
    }
  }

  /// Get all persisted state for debugging
  Future<Map<String, dynamic>> getAllPersistedState() async {
    try {
      final prefs = await _preferences;
      final state = <String, dynamic>{};
      final keys = prefs.getKeys().where((key) => key.startsWith(_keyPrefix));
      
      for (final key in keys) {
        final value = prefs.get(key);
        state[key.replaceFirst(_keyPrefix, '')] = value;
      }
      
      return state;
    } catch (e) {
      logger.e('Failed to get all persisted state: $e');
      return {};
    }
  }

  /// Save generic key-value pair
  Future<void> saveValue<T>(String key, T value) async {
    try {
      final prefs = await _preferences;
      final fullKey = '$_keyPrefix$key';
      
      if (value is String) {
        await prefs.setString(fullKey, value);
      } else if (value is int) {
        await prefs.setInt(fullKey, value);
      } else if (value is double) {
        await prefs.setDouble(fullKey, value);
      } else if (value is bool) {
        await prefs.setBool(fullKey, value);
      } else if (value is List<String>) {
        await prefs.setStringList(fullKey, value);
      } else {
        // For complex objects, serialize to JSON
        await prefs.setString(fullKey, jsonEncode(value));
      }
      
      logger.d('Saved value for key: $key');
    } catch (e) {
      logger.e('Failed to save value for key $key: $e');
    }
  }

  /// Load generic value by key
  Future<T?> loadValue<T>(String key) async {
    try {
      final prefs = await _preferences;
      final fullKey = '$_keyPrefix$key';
      final value = prefs.get(fullKey);
      
      if (value is T) {
        logger.d('Loaded value for key: $key');
        return value;
      } else if (value is String && T != String) {
        // Try to deserialize from JSON
        try {
          final decoded = jsonDecode(value);
          if (decoded is T) {
            logger.d('Loaded and deserialized value for key: $key');
            return decoded;
          }
        } catch (_) {
          // Not JSON, return null
        }
      }
    } catch (e) {
      logger.e('Failed to load value for key $key: $e');
    }
    return null;
  }
}
