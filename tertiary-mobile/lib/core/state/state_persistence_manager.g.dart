// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'state_persistence_manager.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$statePersistenceManagerHash() =>
    r'5579790f2baeedc56c6b749b4a0d2716964b4394';

/// See also [statePersistenceManager].
@ProviderFor(statePersistenceManager)
final statePersistenceManagerProvider =
    AutoDisposeProvider<StatePersistenceManager>.internal(
  statePersistenceManager,
  name: r'statePersistenceManagerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$statePersistenceManagerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef StatePersistenceManagerRef
    = AutoDisposeProviderRef<StatePersistenceManager>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
